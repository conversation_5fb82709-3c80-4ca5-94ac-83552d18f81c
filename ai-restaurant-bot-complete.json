{"name": "AI Restaurant Automation Bot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 400], "webhookId": "restaurant-bot-webhook", "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "start-command-check", "leftValue": "={{ $json.message.text }}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-start-command", "name": "Check Start Command", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "🍽️ **Welcome to Delicious Bites Restaurant!**\n\nI'm your AI ordering assistant, ready to help you with:\n\n🍕 Browse our delicious menu\n🛒 Place orders with natural language\n📍 Calculate delivery fees\n⭐ Get personalized recommendations\n📞 Restaurant information\n\nJust tell me what you'd like to eat, or ask me anything about our menu!\n\n*Example: \"I want 2 pizzas and a coke\" or \"What appetizers do you have?\"*", "additionalFields": {"reply_markup": {"inline_keyboard": [[{"text": "🍽️ View Full Menu", "callback_data": "show_menu"}, {"text": "🛒 My Cart", "callback_data": "show_cart"}], [{"text": "📞 Contact Info", "callback_data": "contact_info"}, {"text": "ℹ️ About Us", "callback_data": "about_us"}]]}, "parse_mode": "<PERSON><PERSON>"}}, "id": "welcome-message", "name": "Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 200], "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "<PERSON><PERSON>", "mode": "name"}, "options": {"range": "A:E"}}, "id": "get-menu-data", "name": "Get Menu Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [460, 500], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "Settings", "mode": "name"}, "options": {"range": "A:B"}}, "id": "get-settings-data", "name": "Get Settings Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [460, 600], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "Customers", "mode": "name"}, "options": {"range": "A:F"}, "filtersUI": {"values": [{"lookupColumn": "Customer_ID", "lookupValue": "={{ $('Telegram Trigger').item.json.message.from.id }}"}]}}, "id": "get-customer-history", "name": "Get Customer History", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [460, 700], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"jsCode": "// Prepare comprehensive RAG context for AI Agent\nconst userMessage = $('Telegram Trigger').item.json.message.text || '';\nconst userId = $('Telegram Trigger').item.json.message.from.id;\nconst userName = $('Telegram Trigger').item.json.message.from.first_name || 'Customer';\nconst chatId = $('Telegram Trigger').item.json.message.chat.id;\n\n// Process menu data\nconst menuData = $('Get Menu Data').all().map(item => item.json).filter(item => item.Category);\nconst menuByCategory = {};\nmenuData.forEach(item => {\n  if (!menuByCategory[item.Category]) {\n    menuByCategory[item.Category] = [];\n  }\n  menuByCategory[item.Category].push({\n    name: item.Item_Name,\n    description: item.Description,\n    price: parseFloat(item.Price),\n    available: item.Available === 'TRUE' || item.Available === true\n  });\n});\n\n// Process settings data\nconst settingsData = $('Get Settings Data').all().map(item => item.json);\nconst settings = {};\nsettingsData.forEach(setting => {\n  if (setting.Setting && setting.Value) {\n    settings[setting.Setting] = setting.Value;\n  }\n});\n\n// Process customer history\nconst customerHistory = $('Get Customer History').all().map(item => item.json);\n\n// Create comprehensive knowledge base\nconst knowledgeBase = {\n  restaurant: {\n    name: settings.Restaurant_Name || 'Delicious Bites Restaurant',\n    address: settings.Restaurant_Address || '123 Food Street, City',\n    minOrderAmount: parseFloat(settings.Min_Order_Amount) || 15.00\n  },\n  menu: {\n    categories: Object.keys(menuByCategory),\n    items: menuByCategory,\n    totalItems: menuData.length\n  },\n  delivery: {\n    zone1Fee: parseFloat(settings.Delivery_Zone_1_Fee) || 2.99,\n    zone2Fee: parseFloat(settings.Delivery_Zone_2_Fee) || 4.99,\n    zone3Fee: parseFloat(settings.Delivery_Zone_3_Fee) || 6.99\n  },\n  customer: {\n    id: userId,\n    name: userName,\n    history: customerHistory,\n    totalOrders: customerHistory.length,\n    isReturning: customerHistory.length > 0\n  },\n  conversation: {\n    currentMessage: userMessage,\n    chatId: chatId,\n    timestamp: new Date().toISOString()\n  }\n};\n\nreturn [{ json: { knowledgeBase, userMessage, userId, userName, chatId } }];"}, "id": "prepare-rag-context", "name": "Prepare RAG Context", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"model": "gemini-1.5-flash", "options": {"temperature": 0.7, "maxTokens": 1000, "topP": 0.9}}, "id": "ai-agent", "name": "AI Restaurant Agent", "type": "n8n-nodes-base.agent", "typeVersion": 1.6, "position": [900, 500], "credentials": {"googleGeminiChatModel": {"id": "gemini-api-credentials", "name": "Google Gemini API"}}}, {"parameters": {"model": "gemini-1.5-flash", "prompt": "=You are an intelligent AI restaurant assistant for {{ $json.knowledgeBase.restaurant.name }}. You have access to real-time restaurant data and customer information.\n\n**RESTAURANT KNOWLEDGE BASE:**\n```json\n{{ JSON.stringify($json.knowledgeBase, null, 2) }}\n```\n\n**CUSTOMER MESSAGE:** \"{{ $json.userMessage }}\"\n**CUSTOMER:** {{ $json.userName }} (ID: {{ $json.userId }})\n\n**YOUR ROLE & CAPABILITIES:**\n🍽️ **Menu Expert**: Know every item, price, and ingredient\n🛒 **Order Assistant**: Process natural language orders intelligently\n📍 **Delivery Calculator**: Determine zones and fees\n⭐ **Personal Recommender**: Use customer history for suggestions\n💬 **Conversational**: Chat naturally, not robotically\n\n**RESPONSE GUIDELINES:**\n1. **Natural Language Processing**: Understand orders like \"I want 2 pizzas and a coke\"\n2. **Menu Queries**: Show relevant items with prices and descriptions\n3. **Order Processing**: Confirm items, quantities, calculate totals\n4. **Personalization**: Reference customer history when available\n5. **Error Handling**: Gracefully handle unavailable items or unclear requests\n6. **Engagement**: Use emojis, be friendly, ask clarifying questions\n\n**RESPONSE FORMAT:**\n- For menu browsing: Show categorized items with prices\n- For orders: Confirm selection and show running total\n- For questions: Provide helpful, accurate information\n- Always end with a helpful suggestion or question\n\n**SPECIAL INSTRUCTIONS:**\n- If customer is returning (has order history), acknowledge them warmly\n- For new customers, offer popular recommendations\n- Always confirm orders before processing\n- Calculate delivery fees based on address/zone\n- Minimum order: ${{ $json.knowledgeBase.restaurant.minOrderAmount }}\n\nRespond as a knowledgeable, friendly restaurant staff member:", "options": {"temperature": 0.7, "maxTokens": 800, "topP": 0.9}}, "id": "gemini-chat-model", "name": "Gemini Chat Model", "type": "n8n-nodes-base.googleGeminiChatModel", "typeVersion": 1.0, "position": [900, 400], "credentials": {"googleGeminiApi": {"id": "gemini-api-credentials", "name": "Google Gemini API"}}}, {"parameters": {"jsCode": "// Process AI response and extract order information\nconst aiResponse = $json.response || $json.text || '';\nconst userMessage = $('Prepare RAG Context').item.json.userMessage;\nconst knowledgeBase = $('Prepare RAG Context').item.json.knowledgeBase;\n\n// Check if this is an order (contains items and quantities)\nconst orderPattern = /\\b(\\d+)\\s+(\\w+)/gi;\nconst orderMatches = userMessage.match(orderPattern);\n\n// Extract potential order items from user message\nconst potentialOrder = [];\nif (orderMatches) {\n  const menuItems = Object.values(knowledgeBase.menu.items).flat();\n  \n  orderMatches.forEach(match => {\n    const [, quantity, itemName] = match.match(/(\\d+)\\s+(\\w+)/);\n    const foundItem = menuItems.find(item => \n      item.name.toLowerCase().includes(itemName.toLowerCase())\n    );\n    \n    if (foundItem) {\n      potentialOrder.push({\n        item: foundItem.name,\n        quantity: parseInt(quantity),\n        price: foundItem.price,\n        total: foundItem.price * parseInt(quantity)\n      });\n    }\n  });\n}\n\n// Calculate order total\nconst orderTotal = potentialOrder.reduce((sum, item) => sum + item.total, 0);\nconst isOrder = potentialOrder.length > 0;\n\n// Determine response type\nlet responseType = 'general';\nif (userMessage.toLowerCase().includes('menu')) responseType = 'menu';\nif (isOrder) responseType = 'order';\nif (userMessage.toLowerCase().includes('cart')) responseType = 'cart';\n\nreturn [{\n  json: {\n    aiResponse,\n    responseType,\n    isOrder,\n    potentialOrder,\n    orderTotal,\n    userMessage,\n    knowledgeBase\n  }\n}];"}, "id": "process-ai-response", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-order-check", "leftValue": "={{ $json.isOrder }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "check-if-order", "name": "Check If Order", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 400]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "Orders", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"Order_ID": "=ORD-{{ new Date().getTime() }}", "Customer_Name": "={{ $('Prepare RAG Context').item.json.userName }}", "Phone": "={{ $('Prepare RAG Context').item.json.userId }}", "Address": "Pending Collection", "Items": "={{ JSON.stringify($json.potentialOrder) }}", "Total_Amount": "={{ $json.orderTotal }}", "Delivery_Fee": "0", "Grand_Total": "={{ $json.orderTotal }}", "Status": "Pending Confirmation", "Timestamp": "={{ new Date().toISOString() }}"}, "matchingColumns": [], "schema": []}, "options": {}}, "id": "save-order-to-sheets", "name": "Save Order to Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [1560, 300], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"chatId": "={{ $('Prepare RAG Context').item.json.chatId }}", "text": "={{ $json.aiResponse }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "reply_markup": {"inline_keyboard": [[{"text": "🍽️ Browse Menu", "callback_data": "show_menu"}, {"text": "🛒 View Cart", "callback_data": "show_cart"}], [{"text": "📞 Contact Us", "callback_data": "contact_info"}, {"text": "✅ Confirm Order", "callback_data": "confirm_order"}]]}}}, "id": "send-telegram-response", "name": "Send Telegram Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 500], "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Check Start Command", "type": "main", "index": 0}, {"node": "Get Menu Data", "type": "main", "index": 0}, {"node": "Get Settings Data", "type": "main", "index": 0}, {"node": "Get Customer History", "type": "main", "index": 0}]]}, "Check Start Command": {"main": [[{"node": "Welcome Message", "type": "main", "index": 0}], [{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Menu Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Settings Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Customer History": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Prepare RAG Context": {"main": [[{"node": "Gemini Chat Model", "type": "main", "index": 0}]]}, "Gemini Chat Model": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Check If Order", "type": "main", "index": 0}, {"node": "Send Telegram Response", "type": "main", "index": 0}]]}, "Check If Order": {"main": [[{"node": "Save Order to Sheets", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-12-27T10:00:00.000Z", "versionId": "1.0"}