{"name": "AI Restaurant Automation Bot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [240, 400], "webhookId": "restaurant-bot-webhook", "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "start-command-check", "leftValue": "={{ $json.message.text }}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-start-command", "name": "Check Start Command", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "🍽️ **Welcome to Delicious Bites Restaurant!**\n\nI'm your AI ordering assistant, ready to help you with:\n\n🍕 Browse our delicious menu\n🛒 Place orders with natural language\n📍 Calculate delivery fees\n⭐ Get personalized recommendations\n📞 Restaurant information\n\nJust tell me what you'd like to eat, or ask me anything about our menu!\n\n*Example: \"I want 2 pizzas and a coke\" or \"What appetizers do you have?\"*", "additionalFields": {"reply_markup": {"inline_keyboard": [[{"text": "🍽️ View Full Menu", "callback_data": "show_menu"}, {"text": "🛒 My Cart", "callback_data": "show_cart"}], [{"text": "📞 Contact Info", "callback_data": "contact_info"}, {"text": "ℹ️ About Us", "callback_data": "about_us"}]]}, "parse_mode": "<PERSON><PERSON>"}}, "id": "welcome-message", "name": "Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 200], "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "<PERSON><PERSON>", "mode": "name"}, "options": {"range": "A:E"}}, "id": "get-menu-data", "name": "Get Menu Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 5.0, "position": [460, 500], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "Settings", "mode": "name"}, "options": {"range": "A:B"}}, "id": "get-settings-data", "name": "Get Settings Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [460, 600], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "Customers", "mode": "name"}, "options": {"range": "A:F"}, "filtersUI": {"values": [{"lookupColumn": "Customer_ID", "lookupValue": "={{ $('Telegram Trigger').item.json.message.from.id }}"}]}}, "id": "get-customer-history", "name": "Get Customer History", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [460, 700], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2"}}}, {"parameters": {"jsCode": "// Prepare comprehensive RAG context for AI Agent\nconst userMessage = $('Telegram Trigger').item.json.message.text || '';\nconst userId = $('Telegram Trigger').item.json.message.from.id;\nconst userName = $('Telegram Trigger').item.json.message.from.first_name || 'Customer';\nconst chatId = $('Telegram Trigger').item.json.message.chat.id;\n\n// Process menu data\nconst menuData = $('Get Menu Data').all().map(item => item.json).filter(item => item.Category);\nconst menuByCategory = {};\nmenuData.forEach(item => {\n  if (!menuByCategory[item.Category]) {\n    menuByCategory[item.Category] = [];\n  }\n  menuByCategory[item.Category].push({\n    name: item.Item_Name,\n    description: item.Description,\n    price: parseFloat(item.Price),\n    available: item.Available === 'TRUE' || item.Available === true\n  });\n});\n\n// Process settings data\nconst settingsData = $('Get Settings Data').all().map(item => item.json);\nconst settings = {};\nsettingsData.forEach(setting => {\n  if (setting.Setting && setting.Value) {\n    settings[setting.Setting] = setting.Value;\n  }\n});\n\n// Process customer history\nconst customerHistory = $('Get Customer History').all().map(item => item.json);\n\n// Create comprehensive knowledge base\nconst knowledgeBase = {\n  restaurant: {\n    name: settings.Restaurant_Name || 'Delicious Bites Restaurant',\n    address: settings.Restaurant_Address || '123 Food Street, City',\n    minOrderAmount: parseFloat(settings.Min_Order_Amount) || 15.00\n  },\n  menu: {\n    categories: Object.keys(menuByCategory),\n    items: menuByCategory,\n    totalItems: menuData.length\n  },\n  delivery: {\n    zone1Fee: parseFloat(settings.Delivery_Zone_1_Fee) || 2.99,\n    zone2Fee: parseFloat(settings.Delivery_Zone_2_Fee) || 4.99,\n    zone3Fee: parseFloat(settings.Delivery_Zone_3_Fee) || 6.99\n  },\n  customer: {\n    id: userId,\n    name: userName,\n    history: customerHistory,\n    totalOrders: customerHistory.length,\n    isReturning: customerHistory.length > 0\n  },\n  conversation: {\n    currentMessage: userMessage,\n    chatId: chatId,\n    timestamp: new Date().toISOString()\n  }\n};\n\nreturn [{ json: { knowledgeBase, userMessage, userId, userName, chatId } }];"}, "id": "prepare-rag-context", "name": "Prepare RAG Context", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"options": {"systemMessage": "You are an intelligent AI restaurant assistant for {{ $('Prepare RAG Context').item.json.knowledgeBase.restaurant.name }}. You have access to real-time restaurant data and customer information through specialized tools.\n\n**RESTAURANT KNOWLEDGE BASE:**\n```json\n{{ JSON.stringify($('Prepare RAG Context').item.json.knowledgeBase, null, 2) }}\n```\n\n**CUSTOMER MESSAGE:** \"{{ $('Prepare RAG Context').item.json.userMessage }}\"\n**CUSTOMER:** {{ $('Prepare RAG Context').item.json.userName }} (ID: {{ $('Prepare RAG Context').item.json.userId }})\n\n**YOUR ROLE & CAPABILITIES:**\n🍽️ **Menu Expert**: Know every item, price, and ingredient\n🛒 **Order Assistant**: Process natural language orders intelligently\n📍 **Delivery Calculator**: Determine zones and fees\n⭐ **Personal Recommender**: Use customer history for suggestions\n💬 **Conversational**: Chat naturally, not robotically\n\n**AVAILABLE TOOLS:**\n- **menu_lookup**: Search and retrieve menu items by category or name\n- **order_processor**: Process and validate customer orders\n- **customer_data**: Access customer history and preferences\n- **delivery_calculator**: Calculate delivery fees based on location\n\n**RESPONSE GUIDELINES:**\n1. **Natural Language Processing**: Understand orders like \"I want 2 pizzas and a coke\"\n2. **Menu Queries**: Use menu_lookup tool to show relevant items with prices and descriptions\n3. **Order Processing**: Use order_processor tool to confirm items, quantities, calculate totals\n4. **Personalization**: Use customer_data tool to reference customer history when available\n5. **Error Handling**: Gracefully handle unavailable items or unclear requests\n6. **Engagement**: Use emojis, be friendly, ask clarifying questions\n\n**RESPONSE FORMAT:**\n- For menu browsing: Show categorized items with prices\n- For orders: Confirm selection and show running total\n- For questions: Provide helpful, accurate information\n- Always end with a helpful suggestion or question\n\n**SPECIAL INSTRUCTIONS:**\n- If customer is returning (has order history), acknowledge them warmly\n- For new customers, offer popular recommendations\n- Always confirm orders before processing\n- Calculate delivery fees based on address/zone\n- Minimum order: ${{ $('Prepare RAG Context').item.json.knowledgeBase.restaurant.minOrderAmount }}\n- ALWAYS use the appropriate tools to access data rather than relying on static information\n- When processing orders, ALWAYS use the order_processor tool to validate and save the order\n\nRespond as a knowledgeable, friendly restaurant staff member and use the available tools to provide accurate, real-time information."}}, "id": "ai-restaurant-agent", "name": "AI Restaurant Agent", "type": "n8n-nodes-langchain.agent", "typeVersion": 1.0, "position": [900, 500]}, {"parameters": {"model": "gemini-1.5-flash-latest", "options": {"temperature": 0.7, "maxTokens": 800, "topP": 0.9}}, "id": "gemini-chat-model", "name": "Gemini Chat Model", "type": "n8n-nodes-langchain.lmchatgooglegemini", "typeVersion": 1.0, "position": [900, 400], "credentials": {"googleGeminiApi": {"id": "gemini-api-credentials", "name": "Google Gemini API"}}}, {"parameters": {"name": "menu_lookup", "description": "Search and retrieve menu items by category or name. Use this tool when customers ask about menu items, prices, or availability.", "resource": "sheet", "operation": "read", "documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "id"}, "options": {"range": "A:F"}}, "id": "menu-lookup-tool", "name": "<PERSON><PERSON>up <PERSON>l", "type": "n8n-nodes-langchain.toolGoogleSheets", "typeVersion": 1.0, "position": [700, 300], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets OAuth2"}}}, {"parameters": {"name": "order_processor", "description": "Process and save customer orders. Use this tool when customers place orders or want to confirm their order details.", "resource": "sheet", "operation": "append", "documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=1", "mode": "id"}, "columns": {"mappingMode": "defineBelow", "value": {"Order_ID": "={{ $fromAI('order_id', 'Unique order identifier', 'string') }}", "Customer_ID": "={{ $fromAI('customer_id', 'Customer Telegram ID', 'string') }}", "Customer_Name": "={{ $fromAI('customer_name', 'Customer name', 'string') }}", "Items": "={{ $fromAI('items', 'Ordered items in JSON format', 'string') }}", "Total_Amount": "={{ $fromAI('total_amount', 'Total order amount', 'number') }}", "Order_Status": "={{ $fromAI('order_status', 'Order status (pending/confirmed/delivered)', 'string') }}", "Order_Date": "={{ $fromAI('order_date', 'Order date and time', 'string') }}", "Delivery_Address": "={{ $fromAI('delivery_address', 'Customer delivery address', 'string') }}", "Delivery_Fee": "={{ $fromAI('delivery_fee', 'Calculated delivery fee', 'number') }}"}}, "options": {}}, "id": "order-processor-tool", "name": "Order Processor Tool", "type": "n8n-nodes-langchain.toolGoogleSheets", "typeVersion": 1.0, "position": [700, 400], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets OAuth2"}}}, {"parameters": {"name": "customer_data", "description": "Access customer history and preferences. Use this tool to provide personalized recommendations based on past orders.", "resource": "sheet", "operation": "read", "documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=2", "mode": "id"}, "options": {"range": "A:E"}}, "id": "customer-data-tool", "name": "Customer Data Tool", "type": "n8n-nodes-langchain.toolGoogleSheets", "typeVersion": 1.0, "position": [700, 500], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets OAuth2"}}}, {"parameters": {"chatId": "={{ $('Prepare RAG Context').item.json.chatId }}", "text": "={{ $json.output }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "reply_markup": {"inline_keyboard": [[{"text": "🍽️ Browse Menu", "callback_data": "show_menu"}, {"text": "🛒 View Cart", "callback_data": "show_cart"}], [{"text": "📞 Contact Us", "callback_data": "contact_info"}, {"text": "✅ Confirm Order", "callback_data": "confirm_order"}]]}}}, "id": "send-telegram-response", "name": "Send Telegram Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 500], "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Check Start Command", "type": "main", "index": 0}, {"node": "Get Menu Data", "type": "main", "index": 0}, {"node": "Get Settings Data", "type": "main", "index": 0}, {"node": "Get Customer History", "type": "main", "index": 0}]]}, "Check Start Command": {"main": [[{"node": "Welcome Message", "type": "main", "index": 0}], [{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Menu Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Settings Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Customer History": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Prepare RAG Context": {"main": [[{"node": "AI Restaurant Agent", "type": "main", "index": 0}]]}, "Gemini Chat Model": {"main": [[{"node": "AI Restaurant Agent", "type": "main", "index": 0}]]}, "Menu Lookup Tool": {"main": [[{"node": "AI Restaurant Agent", "type": "main", "index": 0}]]}, "Order Processor Tool": {"main": [[{"node": "AI Restaurant Agent", "type": "main", "index": 0}]]}, "Customer Data Tool": {"main": [[{"node": "AI Restaurant Agent", "type": "main", "index": 0}]]}, "AI Restaurant Agent": {"main": [[{"node": "Send Telegram Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-12-27T10:00:00.000Z", "versionId": "1.0"}