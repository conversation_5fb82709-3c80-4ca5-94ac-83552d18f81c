{"credentials": [{"id": "telegram-bot-credentials", "name": "Restaurant Bot Telegram API", "type": "telegramApi", "data": {"accessToken": "**********************************************"}, "nodesAccess": [{"nodeType": "n8n-nodes-base.telegram", "user": "owner"}, {"nodeType": "n8n-nodes-base.telegramTrigger", "user": "owner"}]}, {"id": "google-sheets-oauth", "name": "Google Sheets OAuth2", "type": "googleSheetsOAuth2Api", "data": {"clientId": "[YOUR_GOOGLE_CLIENT_ID]", "clientSecret": "[YOUR_GOOGLE_CLIENT_SECRET]", "accessToken": "[GENERATED_DURING_OAUTH]", "refreshToken": "[GENERATED_DURING_OAUTH]", "scope": "https://www.googleapis.com/auth/spreadsheets"}, "nodesAccess": [{"nodeType": "n8n-nodes-base.googleSheets", "user": "owner"}]}, {"id": "gemini-api-credentials", "name": "Google Gemini API", "type": "googleGeminiApi", "data": {"apiKey": "[YOUR_GEMINI_API_KEY_HERE]"}, "nodesAccess": [{"nodeType": "n8n-nodes-base.googleGeminiChatModel", "user": "owner"}, {"nodeType": "n8n-nodes-base.agent", "user": "owner"}]}], "setup_instructions": {"telegram_bot": {"step1": "Go to @BotFather on Telegram", "step2": "Send /newbot command", "step3": "Follow instructions to create bot", "step4": "Copy the bot token provided", "step5": "Paste token in telegram-bot-credentials"}, "google_sheets": {"step1": "Go to Google Cloud Console", "step2": "Create new project or select existing", "step3": "Enable Google Sheets API", "step4": "Create OAuth 2.0 credentials", "step5": "Configure OAuth consent screen", "step6": "Use OAuth flow in n8n to authenticate"}, "gemini_api": {"step1": "Visit https://makersuite.google.com/app/apikey", "step2": "Click 'Create API Key'", "step3": "Copy the generated API key", "step4": "Paste in gemini-api-credentials", "note": "API key starts with 'AIza...'"}}, "required_permissions": {"google_sheets": ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive.file"], "telegram": ["Send messages", "Receive messages", "Manage bot settings"], "gemini": ["Generate text", "Process natural language", "API quota usage"]}, "security_notes": {"telegram_token": "Keep bot token secure - it provides full access to your bot", "google_oauth": "OAuth tokens auto-refresh - no manual intervention needed", "gemini_api": "Monitor API usage to avoid quota limits", "general": "Never share credentials in public repositories or forums"}}