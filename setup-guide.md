# AI Restaurant Automation Bot - Complete Setup Guide

## 🚀 Quick Start Instructions

### Step 1: Import Workflow to n8n Cloud

1. **Login to your n8n Cloud account**
2. **Create New Workflow**
3. **Import JSON**: Copy the contents of `ai-restaurant-bot-complete.json`
4. **Paste** into n8n's import dialog
5. **Save** the workflow

### Step 2: Configure Credentials

#### A. Telegram Bot API Credentials
1. **Credential Name**: `telegram-bot-credentials`
2. **Access Token**: `**********:AAHBehW4VSaeXO3jQg089keawORS-236VhM`
3. **Test Connection** to verify

#### B. Google Sheets OAuth2 API
1. **Credential Name**: `google-sheets-oauth`
2. **Follow OAuth flow** to connect your Google account
3. **Grant permissions** for Google Sheets access
4. **Test** with your spreadsheet ID: `1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4`

#### C. Google Gemini API
1. **Get API Key**: Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. **Create API Key** (starts with "AIza...")
3. **Credential Name**: `gemini-api-credentials`
4. **API Key**: [Your Gemini API Key]

### Step 3: Activate Workflow

1. **Save** all credential configurations
2. **Activate** the workflow (toggle switch)
3. **Test** by sending `/start` to your Telegram bot

## 🧠 AI Features Implemented

### RAG (Retrieval-Augmented Generation)
- **Real-time menu data** retrieval from Google Sheets
- **Customer history** analysis for personalization
- **Restaurant settings** integration for delivery zones
- **Context-aware** responses based on current data

### Natural Language Processing
- **Order understanding**: "I want 2 pizzas and a coke"
- **Menu inquiries**: "What appetizers do you have?"
- **Conversational flow**: Natural back-and-forth dialogue
- **Smart recommendations** based on customer history

### Intelligent Order Processing
- **Automatic item recognition** from natural language
- **Quantity extraction** and price calculation
- **Order confirmation** with running totals
- **Google Sheets integration** for order storage

## 📊 Workflow Architecture

```
Telegram Trigger
    ↓
Check Start Command → Welcome Message
    ↓
[Parallel Data Retrieval]
├── Get Menu Data
├── Get Settings Data  
└── Get Customer History
    ↓
Prepare RAG Context
    ↓
Gemini Chat Model (AI Processing)
    ↓
Process AI Response
    ↓
Check If Order → Save Order to Sheets
    ↓
Send Telegram Response
```

## 🔧 Node Configurations

### Telegram Trigger Node
- **Type**: `n8n-nodes-base.telegramTrigger`
- **Version**: 1.1
- **Updates**: ["message"]
- **Webhook**: Auto-generated

### Google Sheets Nodes
- **Type**: `n8n-nodes-base.googleSheets`
- **Version**: 4.4
- **Operations**: Read (Menu, Settings, Customers), Append (Orders)
- **Range**: Optimized for each sheet structure

### Gemini Chat Model Node
- **Type**: `n8n-nodes-base.googleGeminiChatModel`
- **Version**: 1.0
- **Model**: `gemini-1.5-flash`
- **Temperature**: 0.7
- **Max Tokens**: 800

### Code Nodes
- **RAG Context Preparation**: Processes all data sources
- **Response Processing**: Extracts orders and calculates totals
- **Error Handling**: Graceful fallbacks for edge cases

## 🎯 Key Features

### 1. Intelligent Conversation
- Understands natural language orders
- Provides contextual menu recommendations
- Handles complex multi-item orders
- Maintains conversation flow

### 2. Real-time Data Integration
- Live menu updates from Google Sheets
- Customer history for personalization
- Dynamic pricing and availability
- Restaurant settings integration

### 3. Order Management
- Automatic order extraction from text
- Price calculation and totals
- Order confirmation workflow
- Google Sheets order logging

### 4. Customer Experience
- Personalized greetings for returning customers
- Menu recommendations based on history
- Interactive keyboards for easy navigation
- Rich message formatting with emojis

## 🚨 Troubleshooting

### Common Issues:
1. **Credential Errors**: Ensure all APIs are properly authenticated
2. **Sheet Access**: Verify Google Sheets permissions
3. **Gemini API**: Check API key validity and quota
4. **Telegram Webhook**: Ensure bot token is correct

### Testing Checklist:
- [ ] `/start` command shows welcome message
- [ ] Menu requests return formatted menu
- [ ] Natural language orders are processed
- [ ] Orders are saved to Google Sheets
- [ ] Customer history is retrieved correctly

## 📈 Performance Optimization

- **Parallel data retrieval** for faster response times
- **Efficient Google Sheets queries** with proper ranges
- **Optimized AI prompts** for consistent responses
- **Error handling** to prevent workflow failures

## 🔄 Maintenance

### Regular Tasks:
1. **Monitor API quotas** (Gemini, Google Sheets)
2. **Update menu data** in Google Sheets
3. **Review customer feedback** and order patterns
4. **Optimize AI prompts** based on usage

### Scaling Considerations:
- **Database migration** for high-volume restaurants
- **Caching strategies** for frequently accessed data
- **Load balancing** for multiple restaurant instances
- **Advanced analytics** integration

This workflow provides a complete, production-ready AI restaurant automation system that can be immediately deployed and customized for any restaurant business.
