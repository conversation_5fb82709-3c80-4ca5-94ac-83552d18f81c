{"name": "Restaurant Automation Bot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [240, 300], "webhookId": "telegram-webhook", "credentials": {"telegramApi": {"id": "telegram-credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.message.text}}", "operation": "equal", "value2": "/start"}]}}, "id": "check-start-command", "name": "Check Start Command", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"chatId": "={{$json.message.chat.id}}", "text": "🍽️ Welcome to Delicious Bites Restaurant!\n\nI'm your personal ordering assistant. Here's what I can help you with:\n\n🍕 Browse our delicious menu\n📋 Place your order\n🚚 Calculate delivery fees\n📞 Contact information\n\nType /menu to see our full menu or choose from the options below:", "additionalFields": {"reply_markup": {"keyboard": [[{"text": "🍽️ View Menu"}, {"text": "🛒 My Cart"}], [{"text": "📞 Contact Us"}, {"text": "ℹ️ About Us"}]], "resize_keyboard": true, "one_time_keyboard": false}}}, "id": "welcome-message", "name": "Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [680, 200], "credentials": {"telegramApi": {"id": "telegram-credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.message.text}}", "operation": "contains", "value2": "<PERSON><PERSON>"}]}}, "id": "check-menu-request", "name": "Check Menu Request", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"documentId": {"__rl": true, "value": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "id"}, "options": {}}, "id": "get-menu-data", "name": "Get Menu Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [900, 300], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets OAuth2 API"}}}, {"parameters": {"jsCode": "// Group menu items by category\nconst menuItems = $input.all();\nconst categories = {};\n\n// Group items by category\nmenuItems.forEach(item => {\n  const category = item.json.Category;\n  if (!categories[category]) {\n    categories[category] = [];\n  }\n  categories[category].push({\n    name: item.json.Item_Name,\n    description: item.json.Description,\n    price: item.json.Price,\n    available: item.json.Available\n  });\n});\n\n// Create menu text\nlet menuText = \"🍽️ **DELICIOUS BITES RESTAURANT MENU** 🍽️\\n\\n\";\n\nObject.keys(categories).forEach(category => {\n  // Add category emoji\n  let emoji = \"🍽️\";\n  if (category === \"Appetizers\") emoji = \"🥗\";\n  if (category === \"Mains\") emoji = \"🍕\";\n  if (category === \"Desserts\") emoji = \"🍰\";\n  if (category === \"Beverages\") emoji = \"🥤\";\n  \n  menuText += `\\n${emoji} **${category.toUpperCase()}**\\n`;\n  menuText += \"─────────────────────\\n\";\n  \n  categories[category].forEach(item => {\n    if (item.available) {\n      menuText += `🔸 **${item.name}** - $${item.price}\\n`;\n      menuText += `   ${item.description}\\n\\n`;\n    }\n  });\n});\n\nmenuText += \"\\n📞 To order, simply tell me what you'd like!\\n\";\nmenuText += \"💡 Example: 'I want 2 Chicken Wings and 1 Coca Cola'\";\n\nreturn [{ json: { menuText: menuText, categories: categories } }];"}, "id": "format-menu", "name": "Format Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Telegram Trigger": {"main": [[{"node": "Check Start Command", "type": "main", "index": 0}]]}, "Check Start Command": {"main": [[{"node": "Welcome Message", "type": "main", "index": 0}], [{"node": "Check Menu Request", "type": "main", "index": 0}]]}, "Check Menu Request": {"main": [[{"node": "Get Menu Data", "type": "main", "index": 0}]]}, "Get Menu Data": {"main": [[{"node": "Format Menu", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}