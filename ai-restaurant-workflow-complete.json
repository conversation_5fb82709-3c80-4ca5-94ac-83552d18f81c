{"name": "AI-Powered Restaurant Bot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"documentId": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "sheetName": "<PERSON><PERSON>", "options": {}}, "id": "get-menu-data", "name": "Get Menu Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 200]}, {"parameters": {"documentId": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "sheetName": "Settings", "options": {}}, "id": "get-settings-data", "name": "Get Settings Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 300]}, {"parameters": {"documentId": "1YpslkaQWBhAqC_K6CuDAS3w6ojZaBd6IOyaZbdkVua4", "sheetName": "Customers", "options": {}}, "id": "get-customer-history", "name": "Get Customer History", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [460, 400]}, {"parameters": {"jsCode": "// Prepare RAG context for AI\nconst userMessage = $('Telegram Trigger').item.json.message.text;\nconst userId = $('Telegram Trigger').item.json.message.from.id;\nconst userName = $('Telegram Trigger').item.json.message.from.first_name;\n\n// Get menu data\nconst menuData = $('Get Menu Data').all().map(item => item.json);\n\n// Get settings data\nconst settingsData = $('Get Settings Data').all().map(item => item.json);\nconst settings = {};\nsettingsData.forEach(setting => {\n  settings[setting.Setting] = setting.Value;\n});\n\n// Get customer history\nconst customerHistory = $('Get Customer History').all().map(item => item.json);\n\n// Create knowledge base context\nconst knowledgeBase = {\n  menu: menuData,\n  settings: settings,\n  customerHistory: customerHistory,\n  currentUser: {\n    id: userId,\n    name: userName,\n    message: userMessage\n  }\n};\n\nreturn [{ json: { knowledgeBase, userMessage, userId, userName } }];"}, "id": "prepare-rag-context", "name": "Prepare RAG Context", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"model": "gemini-1.5-flash", "prompt": "You are an intelligent restaurant ordering assistant for 'Delicious Bites Restaurant'. You have access to real-time menu data, customer history, and restaurant settings.\n\nKNOWLEDGE BASE:\n{{JSON.stringify($json.knowledgeBase, null, 2)}}\n\nCUSTOMER MESSAGE: \"{{$json.userMessage}}\"\nCUSTOMER NAME: {{$json.userName}}\n\nINSTRUCTIONS:\n1. Be friendly, helpful, and conversational\n2. Use the menu data to answer questions about items, prices, and availability\n3. Help customers place orders by understanding natural language requests\n4. Calculate totals and suggest delivery fees based on settings\n5. Remember customer preferences from their history\n6. Handle edge cases like out-of-stock items gracefully\n7. Always confirm orders before processing\n8. Use emojis to make responses engaging\n\nRESPONSE FORMAT:\n- For menu inquiries: Show relevant items with prices\n- For orders: Confirm items, quantities, and total cost\n- For general chat: Be helpful and guide toward ordering\n- Always end with a helpful suggestion or question\n\n<PERSON><PERSON><PERSON> naturally as if you're a knowledgeable restaurant staff member:", "options": {"temperature": 0.7, "maxTokens": 500}}, "id": "gemini-ai-agent", "name": "Gemini AI Agent", "type": "n8n-nodes-base.googleGemini", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"chatId": "={{$('Telegram Trigger').item.json.message.chat.id}}", "text": "={{$json.response}}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "id": "send-ai-response", "name": "Send AI Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1120, 300]}], "connections": {"Telegram Trigger": {"main": [[{"node": "Get Menu Data", "type": "main", "index": 0}, {"node": "Get Settings Data", "type": "main", "index": 0}, {"node": "Get Customer History", "type": "main", "index": 0}]]}, "Get Menu Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Settings Data": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Get Customer History": {"main": [[{"node": "Prepare RAG Context", "type": "main", "index": 0}]]}, "Prepare RAG Context": {"main": [[{"node": "Gemini AI Agent", "type": "main", "index": 0}]]}, "Gemini AI Agent": {"main": [[{"node": "Send AI Response", "type": "main", "index": 0}]]}}}